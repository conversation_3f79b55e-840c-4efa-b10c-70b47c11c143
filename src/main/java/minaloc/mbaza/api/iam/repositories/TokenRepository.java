package minaloc.mbaza.api.iam.repositories;

import minaloc.mbaza.api.iam.domain.Token;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TokenRepository extends JpaRepository<Token, Long> {
    @Query("SELECT t FROM Token t WHERE t.user.id = :id AND t.expired = false AND t.revoked = false")
    List<Token> findAllValidTokenByUser(Integer id);

    Optional<Token> findByToken(String token);

    void deleteAllTokensByUserId(Long user_id);
}
