package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;
import java.util.UUID;

public final class CreateRoleDTO {
    private CreateRoleDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            @NotBlank(message = "validation.name.required")
            String name,

            @NotBlank(message = "validation.description.required")
            String description,

            @NotEmpty(message = "validation.permissions.required")
            List<UUID> permissionIds
    ) {
    }
}
