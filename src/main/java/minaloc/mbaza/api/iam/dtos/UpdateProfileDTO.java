package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import org.hibernate.validator.constraints.URL;

import java.util.List;

public final class UpdateProfileDTO {
    private UpdateProfileDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            @NotBlank(message = "error.name.required")
            String name,

            @Email(message = "error.email.invalid")
            String email,

            @Pattern(regexp = "^\\+250\\d{9}$", message = "error.phone.invalid")
            String phoneNumber,

            @NotBlank(message = "error.companyName.required")
            String companyName,

            @NotBlank(message = "error.province.required")
            String province,

            @NotBlank(message = "error.district.required")
            String district,

            @NotBlank(message = "error.sector.required")
            String sector,

            @NotBlank(message = "error.cell.required")
            String cell,

            @NotBlank(message = "error.village.required")
            String village,

            @URL(protocol = "https")
            @NotNull(message = "error.companyTinDocumentUrl.required")
            String companyTinDocumentUrl,

            @NotBlank(message = "error.companyTin.required")
            String companyTin,

            @URL(protocol = "https")
            @NotNull(message = "error.exportLicenseDocumentUrl.required")
            String exportLicenseDocumentUrl,

            @NotBlank(message = "error.exportLicenseCode.required")
            String exportLicenseCode,

            @NotBlank(message = "error.language.required")
            String language,

            List<Long> preferredProducts
    ) {
    }
}