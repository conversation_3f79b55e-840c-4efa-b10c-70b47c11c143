package minaloc.mbaza.api.iam.dtos;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public final class ViewRoleDTO {
    private ViewRoleDTO() {
        // Private constructor to prevent instantiation
    }

    public record Output(
            UUID id,
            String name,
            String description,
            Integer totalAssignees,
            LocalDateTime disabledAt,
            LocalDateTime createdAt,
            List<ViewPermissionDto> permissions
    ) {
        public Output {
            if (permissions == null) {
                permissions = new ArrayList<>();
            }
        }

        public record ViewPermissionDto(
                UUID id,
                String name,
                String description
        ) {
        }
    }
}
