package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import minaloc.mbaza.api.iam.enums.EUserTrackingLevel;

import java.util.List;

public final class RegisterDTO {

    public record Input(
            @NotBlank(message = "Username is mandatory")
            String username,

            @Email(message = "Email is invalid")
            String email,

            @NotBlank(message = "Password is mandatory")
            @Size(min = 4, message = "Password length should be longer")
            String password,

            @NotBlank(message = "Occupation is mandatory")
            String occupation,

            <PERSON><PERSON><PERSON> isAdmin,

            @NotBlank(message = "User tracking level is mandatory")
            EUserTrackingLevel userTrackingLevel,

            @NotBlank(message = "User roles is mandatory")
            List<Integer> roles
    ) {
    }
}
