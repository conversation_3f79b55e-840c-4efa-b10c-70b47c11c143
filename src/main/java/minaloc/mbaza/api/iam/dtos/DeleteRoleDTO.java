package minaloc.mbaza.api.iam.dtos;

import io.swagger.v3.oas.annotations.Parameter;

import java.util.UUID;

public final class DeleteRoleDTO {
    private DeleteRoleDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            @Parameter(description = "Alternative role to assign to users who have this role")
            UUID alternativeRoleId
    ) {
    }
}
