package minaloc.mbaza.api.iam.dtos;

import minaloc.mbaza.api.iam.enums.EUserTrackingLevel;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public final class ViewUserDTO {
    private ViewUserDTO() {
        // Private constructor to prevent instantiation
    }

    public record Output(
            UUID id,
            String username,
            String email,
            String occupation,
            EUserTrackingLevel trackingLevel,
            Boolean isActive,
            Boolean isAdmin,
            LocalDateTime createdAt,
            List<ViewRoleDTO.Output> roles
    ) {
    }
}
