package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.NotBlank;

public final class CreatePermissionDTO {
    private CreatePermissionDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            @NotBlank(message = "validation.name.required")
            String name,

            @NotBlank(message = "validation.description.required")
            String description
    ) {
    }
}
