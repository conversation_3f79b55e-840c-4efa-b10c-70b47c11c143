package minaloc.mbaza.api.iam.dtos;

import java.util.HashSet;
import java.util.Set;

public final class ViewUserProfileDTO {
    private ViewUserProfileDTO() {
        // Private constructor to prevent instantiation
    }

    public record Output(
            Long id,
            String email,
            String phoneNumber,
            String name,
            Set<String> permissions,
            Set<String> roles,
            String language,
            String currency,
            String institution,
            String paymentMethod,
            String paymentMerchantCode
    ) {
        public Output {
            if (permissions == null) {
                permissions = new HashSet<>();
            }
            if (roles == null) {
                roles = new HashSet<>();
            }
        }

        public static Output create(
                Long id, String email, String phoneNumber, String name,
                Set<String> permissions, Set<String> roles
        ) {
            return new Output(id, email, phoneNumber, name, permissions, roles,
                    null, null, null, null, null);
        }
    }
}