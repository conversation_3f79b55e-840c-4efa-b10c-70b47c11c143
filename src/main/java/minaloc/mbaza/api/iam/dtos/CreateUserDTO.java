package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import minaloc.mbaza.api.iam.enums.EUserTrackingLevel;

import java.util.List;
import java.util.UUID;

public final class CreateUserDTO {
    private CreateUserDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            @NotBlank(message = "validation.username.required")
            String username,

            @NotBlank(message = "validation.email.required")
            @Email(message = "validation.email.invalid")
            String email,

            @NotBlank(message = "validation.password.required")
            String password,

            @NotBlank(message = "validation.occupation.required")
            String occupation,

            @NotNull(message = "validation.trackingLevel.required")
            EUserTrackingLevel trackingLevel,

            Boolean isActive,

            Boolean isAdmin,

            List<UUID> roleIds
    ) {
    }
}
