package minaloc.mbaza.api.iam.mappers;

import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.dtos.ViewPermissionDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PermissionMapper {

    public ViewPermissionDTO.Output toViewPermissionDTO(Permission permission) {
        if (permission == null) {
            return null;
        }

        return new ViewPermissionDTO.Output(
                permission.getId(),
                permission.getName(),
                permission.getDescription(),
                permission.getCreatedAt()
        );
    }

    public List<ViewPermissionDTO.Output> toViewPermissionDTOList(List<Permission> permissions) {
        if (permissions == null) {
            return List.of();
        }
        return permissions.stream()
                .map(this::toViewPermissionDTO)
                .toList();
    }
}
