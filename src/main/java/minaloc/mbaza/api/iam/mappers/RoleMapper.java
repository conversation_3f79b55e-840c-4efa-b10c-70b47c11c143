package minaloc.mbaza.api.iam.mappers;

import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RoleMapper {

    private final PermissionMapper permissionMapper;

    public RoleMapper(PermissionMapper permissionMapper) {
        this.permissionMapper = permissionMapper;
    }

    public ViewRoleDTO.Output toViewRoleDTO(Role role) {
        if (role == null) {
            return null;
        }

        List<ViewRoleDTO.Output.ViewPermissionDto> permissions = role.getPermissions() != null
                ? role.getPermissions().stream()
                .map(permission -> new ViewRoleDTO.Output.ViewPermissionDto(
                        permission.getId(),
                        permission.getName(),
                        permission.getDescription()
                ))
                .toList()
                : List.of();

        // TODO: Calculate total assignees from user-role relationships
        Integer totalAssignees = 0;

        return new ViewRoleDTO.Output(
                role.getId(),
                role.getName(),
                role.getDescription(),
                totalAssignees,
                role.getDeletedAt(), // Using deletedAt as disabledAt
                role.getCreatedAt(),
                permissions
        );
    }

    public List<ViewRoleDTO.Output> toViewRoleDTOList(List<Role> roles) {
        if (roles == null) {
            return List.of();
        }
        return roles.stream()
                .map(this::toViewRoleDTO)
                .toList();
    }
}
