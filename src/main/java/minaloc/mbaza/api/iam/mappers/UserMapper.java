package minaloc.mbaza.api.iam.mappers;

import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserMapper {

    private final RoleMapper roleMapper;

    public UserMapper(RoleMapper roleMapper) {
        this.roleMapper = roleMapper;
    }

    public ViewUserDTO.Output toViewUserDTO(User user) {
        if (user == null) {
            return null;
        }

        List<ViewRoleDTO.Output> roles = user.getRoles() != null
                ? user.getRoles().stream()
                        .map(roleMapper::toViewRoleDTO)
                        .toList()
                : List.of();

        return new ViewUserDTO.Output(
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                user.getOccupation(),
                user.getTrackingLevel(),
                user.getIsActive(),
                user.getIsAdmin(),
                user.getCreatedAt(),
                roles);
    }

    public List<ViewUserDTO.Output> toViewUserDTOList(List<User> users) {
        if (users == null) {
            return List.of();
        }
        return users.stream()
                .map(this::toViewUserDTO)
                .toList();
    }
}
