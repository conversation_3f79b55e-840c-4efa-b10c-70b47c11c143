package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.UnauthorizedException;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.mappers.UserMapper;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetCurrentUserUseCase {
    
    private final UserRepository userRepository;
    private final UserMapper userMapper;
    
    public ViewUserDTO.Output execute() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("User not authenticated");
        }
        
        String email = authentication.getName();
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UnauthorizedException("User not found"));
                
        return userMapper.toViewUserDTO(user);
    }
}
