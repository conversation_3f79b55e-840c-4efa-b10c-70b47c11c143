package minaloc.mbaza.api.iam.usecases

import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import rw.esoko._common_.internal.exception.BadRequestException
import rw.esoko._common_.internal.exception.NotFoundException
import rw.esoko._common_.jooq.generated.tables.references.PERMISSIONS
import rw.esoko._common_.jooq.generated.tables.references.ROLES
import rw.esoko._contracts_.users.dtos.CreateRoleDTO
import rw.esoko.users.internal.entities.Permission
import rw.esoko.users.internal.entities.Role
import rw.esoko.users.internal.repositories.RoleRepository

@Service
@Transactional
class CreateRoleUseCase(private val dsl: DSLContext, private val roleRepository: RoleRepository) {
    operator fun invoke(dto: CreateRoleDTO.Input) {
        validateRole(dto)
        validatePermissions(dto.permissionIds)

        val role = Role().apply {
            name = dto.name!!
            description = dto.description
            managed = true
            permissions = dto.permissionIds.map { Permission().apply { this.id = it } }.toMutableSet()
        }
        roleRepository.save(role)
    }

    private fun validatePermissions(permissionIds: List<Long>) {
        val permissions: MutableSet<Long> =
            dsl.select(PERMISSIONS.ID).from(PERMISSIONS).where(PERMISSIONS.ID.`in`(permissionIds))
                .fetchInto(Long::class.java).toMutableSet()
        val missingPermissions = permissionIds - permissions
        if (missingPermissions.isNotEmpty()) {
            throw NotFoundException("error.permissions.not.found", missingPermissions.joinToString(","))
        }
    }

    private fun validateRole(dto: CreateRoleDTO.Input) {
        val roleExists = dsl.fetchExists(ROLES, DSL.trim(ROLES.NAME).equalIgnoreCase(dto.name!!.trim()))
        if (roleExists) {
            throw BadRequestException("error.role.already.exists")
        }
    }
}
