package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.dtos.ViewPermissionDTO;
import minaloc.mbaza.api.iam.mappers.PermissionMapper;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetPermissionByIdUseCase {
    
    private final PermissionRepository permissionRepository;
    private final PermissionMapper permissionMapper;
    
    public ViewPermissionDTO.Output execute(UUID permissionId) {
        Permission permission = permissionRepository.findById(permissionId)
                .orElseThrow(() -> new NotFoundException("Permission not found"));
        return permissionMapper.toViewPermissionDTO(permission);
    }
}
