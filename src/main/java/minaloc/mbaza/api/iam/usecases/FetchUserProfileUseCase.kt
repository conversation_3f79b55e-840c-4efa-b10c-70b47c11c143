package minaloc.mbaza.api.iam.usecases

import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Service
import rw.esoko._common_.internal.exception.BadRequestException
import rw.esoko._common_.internal.exception.UnauthorizedException
import rw.esoko._common_.internal.utils.SecurityContextUtil
import rw.esoko._common_.jooq.generated.tables.Permissions.Companion.PERMISSIONS
import rw.esoko._common_.jooq.generated.tables.Users.Companion.USERS
import rw.esoko._common_.jooq.generated.tables.references.PROFILES
import rw.esoko._common_.jooq.generated.tables.references.ROLES
import rw.esoko._common_.jooq.generated.tables.references.ROLE_PERMISSIONS
import rw.esoko._common_.jooq.generated.tables.references.USER_ROLES
import rw.esoko._contracts_.gateway.models.IamUser
import rw.esoko._contracts_.users.dtos.ViewUserProfileDTO

@Service
class FetchUserProfileUseCase(private val dsl: DSLContext) {
    operator fun invoke(): ViewUserProfileDTO.Output {
        val currentUser: IamUser =
            SecurityContextUtil.getCurrentUser() ?: throw UnauthorizedException("error.unauthorized")

        return retrieveUserProfile(currentUser)
    }

    private fun retrieveUserProfile(currentUser: IamUser) = (dsl.select(
        USERS.ID,
        USERS.PASSWORD,
        USERS.EMAIL,
        USERS.PHONE_NUMBER,
        USERS.NAME,
        USERS.LANGUAGE,
        USERS.CURRENCY,
        USERS.INSTITUTION,
        PROFILES.PAYMENT_METHOD,
        PROFILES.PAYMENT_MERCHANT_CODE,

        DSL.arrayRemove(DSL.arrayAgg(ROLES.NAME), DSL.`val`(null, String::class.java)).`as`("roles"),
        DSL.arrayRemove(DSL.arrayAgg(PERMISSIONS.NAME), DSL.`val`(null, String::class.java)).`as`("permissions"),
    )
        .from(USERS)
        .leftJoin(USER_ROLES).on(USERS.ID.eq(USER_ROLES.USER_ID))
        .leftJoin(ROLES).on(USER_ROLES.ROLE_ID.eq(ROLES.ID))
        .leftJoin(PROFILES).on(PROFILES.ID.eq(USERS.PROFILE_ID))
        .leftJoin(ROLE_PERMISSIONS).on(ROLE_PERMISSIONS.ROLE_ID.eq(ROLES.ID))
        .leftJoin(PERMISSIONS).on(ROLE_PERMISSIONS.PERMISSION_ID.eq(PERMISSIONS.ID))
        .where(USERS.ID.eq(currentUser.id))
        .groupBy(
            USERS.ID,
            PROFILES.PAYMENT_METHOD,
            PROFILES.PAYMENT_MERCHANT_CODE
        )
        .fetchOneInto(ViewUserProfileDTO.Output::class.java)
        ?: throw BadRequestException("error.user.profile.not.found"))
}
