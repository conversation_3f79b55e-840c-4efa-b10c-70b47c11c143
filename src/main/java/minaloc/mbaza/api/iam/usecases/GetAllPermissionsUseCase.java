package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.dtos.ViewPermissionDTO;
import minaloc.mbaza.api.iam.mappers.PermissionMapper;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetAllPermissionsUseCase {
    
    private final PermissionRepository permissionRepository;
    private final PermissionMapper permissionMapper;
    
    public List<ViewPermissionDTO.Output> execute() {
        List<Permission> permissions = permissionRepository.findAll();
        return permissionMapper.toViewPermissionDTOList(permissions);
    }
}
