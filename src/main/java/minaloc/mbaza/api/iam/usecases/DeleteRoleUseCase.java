package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class DeleteRoleUseCase {
    
    private final RoleRepository roleRepository;
    
    public void execute(UUID roleId, DeleteRoleDTO.Input dto) {
        // Find role
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new NotFoundException("Role not found"));
                
        // Check if role is managed
        if (!role.getManaged()) {
            throw new BadRequestException("Cannot delete system role");
        }
        
        // TODO: Check if role has assigned users and handle reassignment
        // For now, we'll just delete the role
        
        roleRepository.delete(role);
    }
}
