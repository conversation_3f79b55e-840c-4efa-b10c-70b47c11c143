package minaloc.mbaza.api.iam.usecases;


import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.RequestPasswordResetCodeDTO;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class RequestPasswordResetUseCase {
    private static final Logger logger = LoggerFactory.getLogger(RequestPasswordResetUseCase.class);

    private final UserRepository userRepository;

    public RequestPasswordResetUseCase(
            UserRepository userRepository
    ) {
        this.userRepository = userRepository;
    }

    public void requestPasswordReset(RequestPasswordResetCodeDTO.Input dto) {
        User user = userRepository.findByEmail(dto.email()).orElseThrow(() -> new NotFoundException("User not found"));
        // Send email
    }
}
