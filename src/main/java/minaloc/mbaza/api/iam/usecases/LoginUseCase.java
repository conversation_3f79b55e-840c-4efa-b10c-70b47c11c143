package minaloc.mbaza.api.iam.usecases;

import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.UnauthorizedException;
import minaloc.mbaza.api.common.services.JwtService;
import minaloc.mbaza.api.iam.domain.Token;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.LoginDTO;
import minaloc.mbaza.api.iam.repositories.TokenRepository;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class LoginUseCase {

    private static final Logger logger = LoggerFactory.getLogger(LoginUseCase.class);

    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final TokenRepository tokenRepository;
    private final UserRepository userRepository;

    public LoginUseCase(
            PasswordEncoder passwordEncoder,
            JwtService jwtService,
            TokenRepository tokenRepository,
            UserRepository userRepository) {
        this.passwordEncoder = passwordEncoder;
        this.jwtService = jwtService;
        this.tokenRepository = tokenRepository;
        this.userRepository = userRepository;
    }

    public LoginDTO.Output login(LoginDTO.Input loginRequestDTO) {
        User user = findVerifiedUser(loginRequestDTO.Email());
        validatePassword(loginRequestDTO.password(), user);
        var token = generateToken(user, loginRequestDTO.Email());
        return new LoginDTO.Output(token);
    }

    private void validatePassword(String inputPassword, User user) {
        if (!passwordEncoder.matches(inputPassword, user.getPassword())) {
            throw new UnauthorizedException("Invalid credentials");
        }
    }

    private String generateToken(User user, String identifier) {
        String jwtToken = jwtService.generateToken(user);
        deleteAllUserTokens(user.getId());
        saveUserToken(user, jwtToken, identifier);
        return jwtToken;
    }

    private User findVerifiedUser(String username) {
        User user = userRepository.findByEmail(username)
                .orElseThrow(() -> new BadRequestException("Invalid credentials"));
        if (!user.getIsActive()) {
            throw new UnauthorizedException("Invalid credentials");
        }
        return user;
    }

    private void deleteAllUserTokens(UUID userId) {
        tokenRepository.deleteAllTokensByUserId(userId);
        logger.debug("Delete all the tokens for user with id {}", userId);
    }

    private void saveUserToken(User user, String jwtToken, String username) {
        Token token = new Token();
        token.setUser(user);
        token.setToken(jwtToken);
        token.setExpired(false);
        token.setRevoked(false);
        tokenRepository.save(token);
    }
}
