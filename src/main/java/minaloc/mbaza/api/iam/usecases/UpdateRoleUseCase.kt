package minaloc.mbaza.api.iam.usecases

import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import rw.esoko._common_.internal.exception.BadRequestException
import rw.esoko._common_.jooq.generated.tables.references.PERMISSIONS
import rw.esoko._common_.jooq.generated.tables.references.ROLES
import rw.esoko._contracts_.users.dtos.CreateRoleDTO
import rw.esoko.users.internal.entities.Permission
import rw.esoko.users.internal.repositories.RoleRepository

@Service
@Transactional
class UpdateRoleUseCase(private val dsl: DSLContext, private val roleRepository: RoleRepository) {
    operator fun invoke(roleId: Long, dto: CreateRoleDTO.Input) {
        validateRoleName(dto, roleId)
        validatePermissions(dto.permissionIds)

        val role = roleRepository.findByIdOrNull(roleId) ?: throw BadRequestException("error.role.not.found")

        if (role.managed.not()) {
            throw BadRequestException("error.role.not.managed")
        }

        val updatedRole = role.apply {
            name = dto.name!!
            description = dto.description
            permissions = dto.permissionIds.map { Permission().apply { this.id = it } }.toMutableSet()
        }
        roleRepository.save(updatedRole)
    }

    private fun validatePermissions(permissionIds: List<Long>) {
        if (permissionIds.isEmpty()) {
            throw BadRequestException("error.permissions.required")
        }
        val permissions: MutableSet<Long> =
            dsl.select(PERMISSIONS.ID).from(PERMISSIONS).where(PERMISSIONS.ID.`in`(permissionIds))
                .fetchInto(Long::class.java).toMutableSet()
        val missingPermissions = permissionIds - permissions
        if (missingPermissions.isNotEmpty()) {
            throw BadRequestException("error.permissions.not.found", missingPermissions.joinToString(","))
        }
    }

    private fun validateRoleName(dto: CreateRoleDTO.Input, roleId: Long) {
        val roleExists =
            dsl.fetchExists(ROLES, DSL.trim(ROLES.NAME).equalIgnoreCase(dto.name!!.trim()).and(ROLES.ID.ne(roleId)))
        if (roleExists) {
            throw BadRequestException("error.role.already.exists")
        }
    }
}
