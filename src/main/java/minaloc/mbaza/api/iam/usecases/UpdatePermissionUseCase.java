package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.dtos.CreatePermissionDTO;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdatePermissionUseCase {
    
    private final PermissionRepository permissionRepository;
    
    public void execute(UUID permissionId, CreatePermissionDTO.Input dto) {
        // Find permission
        Permission permission = permissionRepository.findById(permissionId)
                .orElseThrow(() -> new NotFoundException("Permission not found"));
        
        // Update permission
        permission.setName(dto.name().trim());
        permission.setDescription(dto.description());
        
        try {
            permissionRepository.save(permission);
        } catch (Exception e) {
            // Handle unique constraint violation
            if (e.getMessage().contains("unique") || e.getMessage().contains("duplicate")) {
                throw new BadRequestException("Permission with this name already exists");
            }
            throw e;
        }
    }
}
