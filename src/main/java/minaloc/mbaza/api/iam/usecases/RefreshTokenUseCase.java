package minaloc.mbaza.api.iam.usecases;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.RefreshTokenDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@Transactional
@RequiredArgsConstructor
public class RefreshTokenUseCase {

    private static final Logger logger = LoggerFactory.getLogger(RefreshTokenUseCase.class);

    public RefreshTokenDTO.Output refreshToken(RefreshTokenDTO.Input refreshTokenRequestDTO) {
        return new RefreshTokenDTO.Output("newAccessToken", "newRefreshToken");
    }
}
