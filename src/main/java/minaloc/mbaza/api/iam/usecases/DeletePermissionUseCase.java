package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class DeletePermissionUseCase {
    
    private final PermissionRepository permissionRepository;
    
    public void execute(UUID permissionId) {
        // Find permission
        Permission permission = permissionRepository.findById(permissionId)
                .orElseThrow(() -> new NotFoundException("Permission not found"));
        
        // TODO: Check if permission is used by any roles
        // For now, we'll just delete the permission
        
        try {
            permissionRepository.delete(permission);
        } catch (Exception e) {
            // Handle foreign key constraint violation
            if (e.getMessage().contains("foreign key") || e.getMessage().contains("constraint")) {
                throw new BadRequestException("Cannot delete permission that is assigned to roles");
            }
            throw e;
        }
    }
}
