package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateUserUseCase {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    
    public void execute(UUID userId, UpdateUserDTO.Input dto) {
        // Find user
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NotFoundException("User not found"));
        
        // Check email uniqueness (excluding current user)
        if (userRepository.existsByEmailAndIdNot(dto.email(), userId)) {
            throw new BadRequestException("User with this email already exists");
        }
        
        // Validate roles if provided
        List<Role> roles = List.of();
        if (dto.roleIds() != null && !dto.roleIds().isEmpty()) {
            roles = roleRepository.findAllById(dto.roleIds());
            if (roles.size() != dto.roleIds().size()) {
                throw new BadRequestException("One or more roles not found");
            }
        }
        
        // Update user
        user.setUsername(dto.username());
        user.setEmail(dto.email());
        user.setOccupation(dto.occupation());
        user.setTrackingLevel(dto.trackingLevel());
        user.setIsActive(dto.isActive());
        user.setIsAdmin(dto.isAdmin());
        user.setRoles(roles);
        
        userRepository.save(user);
    }
}
