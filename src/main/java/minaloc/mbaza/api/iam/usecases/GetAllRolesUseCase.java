package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.mappers.RoleMapper;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetAllRolesUseCase {
    
    private final RoleRepository roleRepository;
    private final RoleMapper roleMapper;
    
    public List<ViewRoleDTO.Output> execute() {
        List<Role> roles = roleRepository.findAllManagedRoles();
        return roleMapper.toViewRoleDTOList(roles);
    }
}
