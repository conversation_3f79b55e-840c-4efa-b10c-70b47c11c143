package minaloc.mbaza.api.iam.usecases;

import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.ResetPasswordDTO;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class ResetPasswordUseCase {
    private static final Logger logger = LoggerFactory.getLogger(ResetPasswordUseCase.class);

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    public ResetPasswordUseCase(
            UserRepository userRepository, PasswordEncoder passwordEncoder
    ) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public void resetPassword(ResetPasswordDTO.Input resetPasswordRequestDTO) {
        User user = userRepository.findByEmail("email").orElseThrow(() -> new NotFoundException("User not found"));
        user.setPassword(passwordEncoder.encode(resetPasswordRequestDTO.newPassword()));
        userRepository.save(user);
    }
}
