package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.UnauthorizedException;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateCurrentUserUseCase {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    
    public void execute(UpdateUserDTO.Input dto) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("User not authenticated");
        }
        
        String email = authentication.getName();
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UnauthorizedException("User not found"));
        
        // Check email uniqueness (excluding current user)
        if (userRepository.existsByEmailAndIdNot(dto.email(), user.getId())) {
            throw new BadRequestException("User with this email already exists");
        }
        
        // Validate roles if provided (only allow users to update certain fields)
        // For security, we might want to restrict role changes for current user
        List<Role> roles = user.getRoles(); // Keep existing roles
        if (dto.roleIds() != null && !dto.roleIds().isEmpty()) {
            // Only allow role changes if user is admin or has permission
            if (user.getIsAdmin()) {
                roles = roleRepository.findAllById(dto.roleIds());
                if (roles.size() != dto.roleIds().size()) {
                    throw new BadRequestException("One or more roles not found");
                }
            }
        }
        
        // Update user (restrict certain fields for security)
        user.setUsername(dto.username());
        user.setEmail(dto.email());
        user.setOccupation(dto.occupation());
        user.setTrackingLevel(dto.trackingLevel());
        // Don't allow users to change their own admin status or active status
        // user.setIsActive(dto.isActive());
        // user.setIsAdmin(dto.isAdmin());
        user.setRoles(roles);
        
        userRepository.save(user);
    }
}
