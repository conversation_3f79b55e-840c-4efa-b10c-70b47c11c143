package minaloc.mbaza.api.iam.usecases

import org.jooq.DSLContext
import org.springframework.stereotype.Service
import rw.esoko._common_.jooq.generated.tables.references.PERMISSIONS
import rw.esoko._contracts_.users.dtos.ViewRoleDTO

@Service
class FetchPermissionsUseCase(private val dsl: DSLContext) {
    operator fun invoke(): List<ViewRoleDTO.Output.ViewPermissionDto> {
        return dsl.select(
            PERMISSIONS.ID, PERMISSIONS.NAME, PERMISSIONS.DESCRIPTION
        ).from(PERMISSIONS).fetchInto(ViewRoleDTO.Output.ViewPermissionDto::class.java)
    }
}
