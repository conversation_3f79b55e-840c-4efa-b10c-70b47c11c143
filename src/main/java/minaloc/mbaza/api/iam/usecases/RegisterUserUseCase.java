package minaloc.mbaza.api.iam.usecases;

import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.RegisterDTO;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class RegisterUserUseCase {
    private static final Logger logger = LoggerFactory.getLogger(RegisterUserUseCase.class);

    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;

    public RegisterUserUseCase(
            PasswordEncoder passwordEncoder,
            UserRepository userRepository
    ) {
        this.passwordEncoder = passwordEncoder;
        this.userRepository = userRepository;
    }

    public void registerUser(RegisterDTO.Input dto) {
        User user = new User();
        user.setUsername(dto.username());
        user.setEmail(dto.email());
        user.setPassword(passwordEncoder.encode(dto.password()));
        user.setOccupation(dto.occupation());
        user.setIsActive(true);
        user.setIsAdmin(dto.isAdmin());
        user.setTrackingLevel(dto.userTrackingLevel());
        userRepository.save(user);
    }
}
