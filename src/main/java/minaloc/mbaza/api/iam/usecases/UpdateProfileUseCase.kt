package minaloc.mbaza.api.iam.usecases

import org.jooq.Condition
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import rw.esoko._common_.internal.exception.BadRequestException
import rw.esoko._common_.internal.exception.NotFoundException
import rw.esoko._common_.internal.extensions.findEnumValue
import rw.esoko._common_.internal.utils.SecurityContextUtil
import rw.esoko._common_.jooq.generated.tables.Users.Companion.USERS
import rw.esoko._contracts_.users.dtos.UpdateExporterProfileDTO
import rw.esoko.users.internal.entities.Profile
import rw.esoko.users.internal.entities.User
import rw.esoko.users.internal.enums.ELanguage
import rw.esoko.users.internal.repositories.ProfileRepository
import rw.esoko.users.internal.repositories.UserRepository

@Service
@Transactional
class UpdateProfileUseCase(
    private val dsl: DSLContext,
    private val userRepository: UserRepository,
    private val profileRepository: ProfileRepository,
//    private val addPreferredProductsUseCase: AddPreferredProductsUseCase
) {

    operator fun invoke(dto: UpdateExporterProfileDTO.Input) {
        val currentUser = SecurityContextUtil.getCurrentUser()!!

        validateDtoData(dto, currentUser.id!!)

        val dbUser = retrieveUser(currentUser.id!!)
        val user = mapDtoToUser(dbUser, dto)

        val profile = mapDtoToProfile(user.profile!!, dto)

//        //TODO REMOVE PREFERRED PRODUCTS USE EVENTS TO ADD PREFERRED PRODUCTS
//        addPreferredProductsUseCase(AddPreferredProductsDTO.Input(dto.preferredProducts!!), currentUser.id!!)

        userRepository.save(user)
        profileRepository.save(profile)
    }

    private fun validateDtoData(dto: UpdateExporterProfileDTO.Input, userId: Long) {
        validateAndGetLanguage(dto.language!!)
        validateTinDocument(dto.companyTinDocumentUrl!!)
        validateExportLicenseDocument(dto.exportLicenseDocumentUrl!!)
        validateEmailAndPhoneNumberNotTaken(dto.email, dto.phoneNumber, userId)
    }

    private fun mapDtoToUser(dbUser: User, dto: UpdateExporterProfileDTO.Input): User = dbUser.apply {
        this.name = dto.name!!
        this.email = dto.email
        this.phoneNumber = dto.phoneNumber
        this.language = findEnumValue<ELanguage>(dto.language, ELanguage.EN)!!
    }

    private fun retrieveUser(userId: Long): User =
        userRepository.findById(userId).orElseThrow { NotFoundException("error.user.not.found") }

    private fun mapDtoToProfile(profile: Profile, dto: UpdateExporterProfileDTO.Input): Profile = profile.apply {
        this.organization = dto.companyName
        this.province = dto.province
        this.district = dto.district
        this.sector = dto.sector
        this.cell = dto.cell
        this.village = dto.village
        this.taxIdentificationNumber = dto.companyTin
        this.exportLicenseCode = dto.exportLicenseCode
    }

    private fun validateEmailAndPhoneNumberNotTaken(email: String?, phoneNumber: String?, userId: Long) {
        val condition: Condition = when {
            email != null && phoneNumber != null -> USERS.EMAIL.eq(email).or(USERS.PHONE_NUMBER.eq(phoneNumber))
            email != null -> USERS.EMAIL.eq(email)
            phoneNumber != null -> USERS.PHONE_NUMBER.eq(phoneNumber)
            else -> return
        }
        val result = dsl.select(USERS.EMAIL, USERS.PHONE_NUMBER)
            .from(USERS)
            .where(USERS.ID.ne(userId)).and(condition)
            .limit(1)
            .fetchOne()

        if (result == null) return

        if (result[USERS.EMAIL] == email) {
            throw BadRequestException("error.email.taken")
        }

        throw BadRequestException("error.phoneNumber.taken")
    }

    private fun validateExportLicenseDocument(exportLicenseDocumentUrl: String) {
        if (!exportLicenseDocumentUrl.startsWith("https://")) {
            throw BadRequestException("error.exportLicenseDocumentUrl.invalid")
        }
    }

    private fun validateTinDocument(companyTinDocumentUrl: String) {
        if (!companyTinDocumentUrl.startsWith("https://")) {
            throw BadRequestException("error.companyTinDocumentUrl.invalid")
        }
    }

    private fun validateAndGetLanguage(language: String) {
        val supportedLanguages = listOf("en", "fr", "rw")
        if (language.lowercase() !in supportedLanguages) {
            throw BadRequestException("error.language.not.supported")
        }
    }
}