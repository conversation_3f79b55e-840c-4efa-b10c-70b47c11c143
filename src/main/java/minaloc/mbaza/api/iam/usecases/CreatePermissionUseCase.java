package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.dtos.CreatePermissionDTO;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class CreatePermissionUseCase {
    
    private final PermissionRepository permissionRepository;
    
    public void execute(CreatePermissionDTO.Input dto) {
        // Basic validation - check if permission with same name exists
        // Note: We don't have a method for this in the repository yet
        // For now, we'll rely on the database unique constraint
        
        Permission permission = Permission.builder()
                .name(dto.name().trim())
                .description(dto.description())
                .build();
                
        try {
            permissionRepository.save(permission);
        } catch (Exception e) {
            // Handle unique constraint violation
            if (e.getMessage().contains("unique") || e.getMessage().contains("duplicate")) {
                throw new BadRequestException("Permission with this name already exists");
            }
            throw e;
        }
    }
}
