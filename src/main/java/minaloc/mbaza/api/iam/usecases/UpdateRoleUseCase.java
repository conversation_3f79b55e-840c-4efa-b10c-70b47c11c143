package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateRoleUseCase {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    public void execute(UUID roleId, CreateRoleDTO.Input dto) {
        // Find role
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new NotFoundException("Role not found"));

        // Check if role is managed
        if (!role.getManaged()) {
            throw new BadRequestException("Cannot update system role");
        }

        // Check name uniqueness (excluding current role)
        if (roleRepository.existsByNameIgnoreCaseAndIdNot(dto.name().trim(), roleId)) {
            throw new BadRequestException("Role with this name already exists");
        }

        // Validate permissions exist
        List<Permission> permissions = permissionRepository.findAllById(dto.permissionIds());
        if (permissions.size() != dto.permissionIds().size()) {
            throw new BadRequestException("One or more permissions not found");
        }

        // Update role
        role.setName(dto.name().trim());
        role.setDescription(dto.description());
        role.setPermissions(permissions);

        roleRepository.save(role);
    }
}
