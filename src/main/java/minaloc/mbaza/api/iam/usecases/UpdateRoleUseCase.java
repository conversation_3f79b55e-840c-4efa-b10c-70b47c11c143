package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateRoleUseCase {
    
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    
    public void execute(UUID roleId, CreateRoleDTO.Input dto) {
        validateRoleName(dto, roleId);
        validatePermissions(dto.permissionIds());
        
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new BadRequestException("error.role.not.found"));
                
        if (!role.getManaged()) {
            throw new BadRequestException("error.role.not.managed");
        }
        
        role.setName(dto.name());
        role.setDescription(dto.description());
        role.setPermissions(getPermissionsByIds(dto.permissionIds()));
        
        roleRepository.save(role);
    }

    private void validatePermissions(List<UUID> permissionIds) {
        if (permissionIds.isEmpty()) {
            throw new BadRequestException("error.permissions.required");
        }
        
        Set<UUID> existingPermissionIds = permissionRepository.findAllById(permissionIds)
                .stream()
                .map(Permission::getId)
                .collect(Collectors.toSet());
                
        Set<UUID> missingPermissions = new HashSet<>(permissionIds);
        missingPermissions.removeAll(existingPermissionIds);
        
        if (!missingPermissions.isEmpty()) {
            throw new BadRequestException("error.permissions.not.found: " + 
                    missingPermissions.stream()
                            .map(UUID::toString)
                            .collect(Collectors.joining(",")));
        }
    }

    private void validateRoleName(CreateRoleDTO.Input dto, UUID roleId) {
        boolean roleExists = roleRepository.existsByNameIgnoreCaseAndIdNot(dto.name().trim(), roleId);
        if (roleExists) {
            throw new BadRequestException("error.role.already.exists");
        }
    }
    
    private List<Permission> getPermissionsByIds(List<UUID> permissionIds) {
        return permissionRepository.findAllById(permissionIds);
    }
}
