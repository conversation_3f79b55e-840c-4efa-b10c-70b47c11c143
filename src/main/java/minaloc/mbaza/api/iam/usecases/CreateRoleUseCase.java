package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class CreateRoleUseCase {
    
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    
    public void execute(CreateRoleDTO.Input dto) {
        validateRole(dto);
        validatePermissions(dto.permissionIds());
        
        Role role = Role.builder()
                .name(dto.name())
                .description(dto.description())
                .permissions(getPermissionsByIds(dto.permissionIds()))
                .build();
                
        roleRepository.save(role);
    }

    private void validatePermissions(List<UUID> permissionIds) {
        Set<UUID> existingPermissionIds = permissionRepository.findAllById(permissionIds)
                .stream()
                .map(Permission::getId)
                .collect(Collectors.toSet());
                
        Set<UUID> missingPermissions = new HashSet<>(permissionIds);
        missingPermissions.removeAll(existingPermissionIds);
        
        if (!missingPermissions.isEmpty()) {
            throw new NotFoundException("error.permissions.not.found: " + 
                    missingPermissions.stream()
                            .map(UUID::toString)
                            .collect(Collectors.joining(",")));
        }
    }

    private void validateRole(CreateRoleDTO.Input dto) {
        boolean roleExists = roleRepository.existsByNameIgnoreCase(dto.name().trim());
        if (roleExists) {
            throw new BadRequestException("error.role.already.exists");
        }
    }
    
    private List<Permission> getPermissionsByIds(List<UUID> permissionIds) {
        return permissionRepository.findAllById(permissionIds);
    }
}
