package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional
public class CreateRoleUseCase {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    public void execute(CreateRoleDTO.Input dto) {
        // Basic validation
        if (roleRepository.existsByNameIgnoreCase(dto.name().trim())) {
            throw new BadRequestException("Role with this name already exists");
        }

        // Validate permissions exist
        List<Permission> permissions = permissionRepository.findAllById(dto.permissionIds());
        if (permissions.size() != dto.permissionIds().size()) {
            throw new BadRequestException("One or more permissions not found");
        }

        // Create role
        Role role = Role.builder()
                .name(dto.name().trim())
                .description(dto.description())
                .permissions(permissions)
                .managed(true)
                .build();

        roleRepository.save(role);
    }
}
