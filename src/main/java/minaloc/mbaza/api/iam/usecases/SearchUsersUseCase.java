package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.mappers.UserMapper;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class SearchUsersUseCase {
    
    private final UserRepository userRepository;
    private final UserMapper userMapper;
    
    public List<ViewUserDTO.Output> execute(String searchTerm) {
        List<User> users = userRepository.searchByEmailOrUsername(searchTerm);
        return userMapper.toViewUserDTOList(users);
    }
}
