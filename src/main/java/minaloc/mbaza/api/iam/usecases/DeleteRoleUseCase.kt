package minaloc.mbaza.api.iam.usecases

import com.fasterxml.jackson.databind.ObjectMapper
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import rw.esoko._common_.internal.exception.BadRequestException
import rw.esoko._common_.internal.exception.NotFoundException
import rw.esoko._common_.internal.utils.SecurityContextUtil
import rw.esoko._common_.jooq.generated.tables.Roles.Companion.ROLES
import rw.esoko._common_.jooq.generated.tables.UserRoles.Companion.USER_ROLES
import rw.esoko._common_.jooq.generated.tables.references.TOKEN
import rw.esoko._contracts_.users.dtos.DeleteRoleDTO
import rw.esoko.users.internal.repositories.RoleRepository

@Service
class DeleteRoleUseCase(private val dsl: DSLContext, private val roleRepository: RoleRepository) {

    @Transactional
    operator fun invoke(roleId: Long, dto: DeleteRoleDTO.Input?) {
        val assignees = validateRole(roleId, dto)

        val currentUser = SecurityContextUtil.getCurrentUser()!!

        if (assignees.contains(currentUser.id)) {
            throw BadRequestException("error.cannot.delete.own.role")
        }

        roleRepository.deleteById(roleId)
        if (assignees.isNotEmpty()) {
            dsl.update(USER_ROLES).set(USER_ROLES.ROLE_ID, dto!!.alternativeRoleId).where(USER_ROLES.ROLE_ID.eq(roleId))
                .execute()

            deleteAllTokens(assignees)
        }
    }

    private fun deleteAllTokens(assignees: Array<Long>) {
        dsl.deleteFrom(TOKEN).where(TOKEN.USER_ID.`in`(*assignees)).execute()
    }

    private fun validateRole(roleId: Long, dto: DeleteRoleDTO.Input?): Array<Long> {
        val (_, assignees) = dsl.select(ROLES.ID, DSL.jsonArrayAgg(USER_ROLES.USER_ID).absentOnNull())
            .from(ROLES)
            .leftJoin(USER_ROLES).on(USER_ROLES.ROLE_ID.eq(ROLES.ID))
            .where(ROLES.ID.eq(roleId).and(ROLES.MANAGED.isTrue))
            .groupBy(ROLES.ID)
            .fetchOne() ?: throw NotFoundException("error.role.not.found", roleId)

        if (assignees == null || assignees.data() == "[]") {
            return emptyArray()
        }

        if (dto?.alternativeRoleId == null) {
            throw BadRequestException("error.role.alternative.required", roleId)
        }

        if (dto.alternativeRoleId == roleId) {
            throw BadRequestException("error.role.alternative.same", roleId)
        }

        val alternativeRoleExists = dsl.fetchExists(ROLES, ROLES.ID.eq(dto.alternativeRoleId).and(ROLES.MANAGED.isTrue))

        if (!alternativeRoleExists) {
            throw NotFoundException("error.role.not.found", dto.alternativeRoleId!!)
        }

        return ObjectMapper().readValue(assignees.data(), Array<Long>::class.java)
    }
}
