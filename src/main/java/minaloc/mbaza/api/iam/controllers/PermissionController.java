package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import minaloc.mbaza.api.common.annotations.ApplySecurityRequirements;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.FetchPermissionsDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.services.PermissionService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/permissions")
@ApplySecurityRequirements
@Tag(name = "Permissions", description = "Operations pertaining to permissions")
public class PermissionController {

    private final PermissionService permissionService;

    public PermissionController(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @GetMapping
    @Operation(summary = "Get all permissions")
    public ResponseEntity<GenericResponse<List<ViewRoleDTO.Output.ViewPermissionDto>>> getPermissions() {
        List<ViewRoleDTO.Output.ViewPermissionDto> permissions = permissionService.fetchPermissions();
        return GenericResponse.ok("success.permissions.fetched", permissions);
    }

    @GetMapping("/me")
    @Operation(summary = "Get current user's permissions")
    public ResponseEntity<GenericResponse<List<FetchPermissionsDTO.Output>>> getMyPermissions() {
        List<FetchPermissionsDTO.Output> permissions = permissionService.fetchAdminPermissions();
        return GenericResponse.ok("success.permissions.fetched", permissions);
    }
}
