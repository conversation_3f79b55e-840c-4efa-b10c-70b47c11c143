package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.ApplySecurityRequirements;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.CreatePermissionDTO;
import minaloc.mbaza.api.iam.dtos.ViewPermissionDTO;
import minaloc.mbaza.api.iam.services.PermissionService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/permissions")
@ApplySecurityRequirements
@Tag(name = "Permissions", description = "Operations pertaining to permissions")
@RequiredArgsConstructor
public class PermissionController {

    private final PermissionService permissionService;

    @PostMapping
    @Operation(summary = "Create permission")
    public ResponseEntity<GenericResponse<Void>> createPermission(@Valid @RequestBody CreatePermissionDTO.Input dto) {
        permissionService.createPermission(dto);
        return GenericResponse.ok("success.permission.created");
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update permission")
    public ResponseEntity<GenericResponse<Void>> updatePermission(
            @PathVariable UUID id,
            @Valid @RequestBody CreatePermissionDTO.Input dto) {
        permissionService.updatePermission(id, dto);
        return GenericResponse.ok("success.permission.updated");
    }

    @GetMapping
    @Operation(summary = "Get all permissions")
    public ResponseEntity<GenericResponse<List<ViewPermissionDTO.Output>>> getAllPermissions() {
        List<ViewPermissionDTO.Output> permissions = permissionService.getAllPermissions();
        return GenericResponse.ok("success.permissions.fetched", permissions);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get permission by ID")
    public ResponseEntity<GenericResponse<ViewPermissionDTO.Output>> getPermissionById(@PathVariable UUID id) {
        ViewPermissionDTO.Output permission = permissionService.getPermissionById(id);
        return GenericResponse.ok("success.permission.fetched", permission);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete permission")
    public ResponseEntity<GenericResponse<Void>> deletePermission(@PathVariable UUID id) {
        permissionService.deletePermission(id);
        return GenericResponse.ok("success.permission.deleted");
    }
}
