package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.ApplySecurityRequirements;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.CreateUserDTO;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.services.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/users")
@ApplySecurityRequirements
@Tag(name = "User Management", description = "User management operations")
@RequiredArgsConstructor
public class UserManagementController {

    private final UserService userService;

    @PostMapping
    @Operation(summary = "Create user")
    public ResponseEntity<GenericResponse<Void>> createUser(@Valid @RequestBody CreateUserDTO.Input dto) {
        userService.createUser(dto);
        return GenericResponse.ok("success.user.created");
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update user")
    public ResponseEntity<GenericResponse<Void>> updateUser(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateUserDTO.Input dto) {
        userService.updateUser(id, dto);
        return GenericResponse.ok("success.user.updated");
    }

    @GetMapping
    @Operation(summary = "Get all users")
    public ResponseEntity<GenericResponse<List<ViewUserDTO.Output>>> getAllUsers(
            @RequestParam(required = false) String search) {
        List<ViewUserDTO.Output> users;
        if (search != null && !search.trim().isEmpty()) {
            users = userService.searchUsers(search.trim());
        } else {
            users = userService.getAllUsers();
        }
        return GenericResponse.ok("success.users.fetched", users);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get user by ID")
    public ResponseEntity<GenericResponse<ViewUserDTO.Output>> getUserById(@PathVariable UUID id) {
        ViewUserDTO.Output user = userService.getUserById(id);
        return GenericResponse.ok("success.user.fetched", user);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete user")
    public ResponseEntity<GenericResponse<Void>> deleteUser(@PathVariable UUID id) {
        userService.deleteUser(id);
        return GenericResponse.ok("success.user.deleted");
    }
}
