package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.*;
import minaloc.mbaza.api.iam.services.IamService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Authentication", description = "Authentication endpoints")
@PublicEndpoint
public class AuthController {

    private final IamService service;

    public AuthController(IamService service) {
        this.service = service;
    }

    @Operation(summary = "Register a new user")
    @PostMapping("/register")
    public ResponseEntity<GenericResponse<Void>> registerUser(
            @Valid @RequestBody RegisterDTO.Input registerRequestDTO
    ) {
        service.registerUser(registerRequestDTO);
        return GenericResponse.ok("User registered successfully");
    }

    @Operation(summary = "Login a user")
    @PostMapping("/login")
    public ResponseEntity<GenericResponse<LoginDTO.Output>> authenticateUser(@Valid @RequestBody LoginDTO.Input loginRequestDTO) {
        LoginDTO.Output loginResponse = service.login(loginRequestDTO);
        return GenericResponse.ok("success.authenticated", loginResponse);
    }

    @Operation(summary = "Refresh token")
    @PostMapping("/refresh-token")
    public ResponseEntity<GenericResponse<RefreshTokenDTO.Output>> refreshToken(@Valid @RequestBody RefreshTokenDTO.Input refreshTokenRequestDTO) {
        RefreshTokenDTO.Output loginResponse = service.refreshToken(refreshTokenRequestDTO);
        return GenericResponse.ok("success.token.refreshed", loginResponse);
    }

    @Operation(summary = "Request reset password code")
    @PostMapping("/request-reset-code")
    public ResponseEntity<GenericResponse<Void>> requestResetPassword(@Valid @RequestBody RequestPasswordResetCodeDTO.Input requestPasswordResetCodeDTO) {
        service.requestPasswordResetCode(requestPasswordResetCodeDTO);
        return GenericResponse.ok("success.reset.code.sent");
    }

    @Operation(summary = "Reset password")
    @PostMapping("/reset-password")
    public ResponseEntity<GenericResponse<Void>> resetPassword(@Valid @RequestBody ResetPasswordDTO.Input resetPasswordRequestDTO) {
        service.resetPassword(resetPasswordRequestDTO);
        return GenericResponse.ok("success.password.reset");
    }
}
