package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.IsAuthenticated;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.services.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/v1/user")
@Tag(name = "Current User", description = "Current user profile endpoints")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @GetMapping("/me")
    @Operation(summary = "Get current user profile")
    @IsAuthenticated
    public ResponseEntity<GenericResponse<ViewUserDTO.Output>> getCurrentUser() {
        UUID currentUserId = getCurrentUserId();
        ViewUserDTO.Output user = userService.getUserById(currentUserId);
        return GenericResponse.ok("success.user.profile.fetched", user);
    }

    @PutMapping("/me")
    @Operation(summary = "Update current user profile")
    @IsAuthenticated
    public ResponseEntity<GenericResponse<Void>> updateCurrentUser(@Valid @RequestBody UpdateUserDTO.Input dto) {
        UUID currentUserId = getCurrentUserId();
        userService.updateUser(currentUserId, dto);
        return GenericResponse.ok("success.user.profile.updated");
    }
}
