package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import minaloc.mbaza.api.common.annotations.IsAuthenticated;
import minaloc.mbaza.api.common.responses.GenericResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/users")
@Tag(name = "Users", description = "User endpoints")
public class UserController {

    @GetMapping("/me")
    @Operation(summary = "Get current user")
    @IsAuthenticated
    public ResponseEntity<GenericResponse<Void>> getUserProfile() {
        return "Hello World";
    }
}
