package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.IsAuthenticated;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.services.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user")
@Tag(name = "Current User", description = "Current user profile endpoints")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @GetMapping("/me")
    @Operation(summary = "Get current user profile")
    @IsAuthenticated
    public ResponseEntity<GenericResponse<ViewUserDTO.Output>> getCurrentUser() {
        ViewUserDTO.Output user = userService.getCurrentUser();
        return GenericResponse.ok("success.user.profile.fetched", user);
    }

    @PatchMapping("/me")
    @Operation(summary = "Update current user profile")
    @IsAuthenticated
    public ResponseEntity<GenericResponse<Void>> updateCurrentUser(@Valid @RequestBody UpdateUserDTO.Input dto) {
        userService.updateCurrentUser(dto);
        return GenericResponse.ok("success.user.profile.updated");
    }
}
