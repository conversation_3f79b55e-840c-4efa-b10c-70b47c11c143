package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import minaloc.mbaza.api.common.annotations.ApplySecurityRequirements;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.services.RoleService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/roles")
@ApplySecurityRequirements
@Tag(name = "Roles management")
@Validated
public class RoleController {

    private final RoleService roleService;

    public RoleController(RoleService roleService) {
        this.roleService = roleService;
    }

    @PostMapping
    @Operation(summary = "Create role")
    public ResponseEntity<GenericResponse<Void>> createRole(@Valid @RequestBody CreateRoleDTO.Input dto) {
        roleService.createRole(dto);
        return GenericResponse.ok("success.role.created");
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update role")
    public ResponseEntity<GenericResponse<Void>> updateRole(
            @PathVariable @Positive Long id,
            @Valid @RequestBody CreateRoleDTO.Input dto
    ) {
        roleService.updateRole(id, dto);
        return GenericResponse.ok("success.role.updated");
    }

    @GetMapping
    @Operation(summary = "Fetch roles with permissions")
    public ResponseEntity<GenericResponse<List<ViewRoleDTO.Output>>> fetchRoles() {
        List<ViewRoleDTO.Output> roles = roleService.fetchManagedRoles();
        return GenericResponse.ok("success.role.fetched", roles);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete role")
    public ResponseEntity<GenericResponse<Void>> deleteRole(
            @PathVariable @Positive Long id,
            @RequestBody(required = false) DeleteRoleDTO.Input dto
    ) {
        roleService.deleteRole(id, dto);
        return GenericResponse.ok("success.role.deleted");
    }

    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "Toggle role status")
    public ResponseEntity<GenericResponse<Void>> toggleRoleStatus(@PathVariable @Positive Long id) {
        roleService.toggleRoleStatus(id);
        return GenericResponse.ok("success.role.status.toggled");
    }
}
