package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.ApplySecurityRequirements;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.services.RoleService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/roles")
@ApplySecurityRequirements
@Tag(name = "Roles management")
@Validated
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;

    @PostMapping
    @Operation(summary = "Create role")
    public ResponseEntity<GenericResponse<Void>> createRole(@Valid @RequestBody CreateRoleDTO.Input dto) {
        roleService.createRole(dto);
        return GenericResponse.ok("success.role.created");
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update role")
    public ResponseEntity<GenericResponse<Void>> updateRole(
            @PathVariable UUID id,
            @Valid @RequestBody CreateRoleDTO.Input dto) {
        roleService.updateRole(id, dto);
        return GenericResponse.ok("success.role.updated");
    }

    @GetMapping
    @Operation(summary = "Fetch all roles")
    public ResponseEntity<GenericResponse<List<ViewRoleDTO.Output>>> getAllRoles() {
        List<ViewRoleDTO.Output> roles = roleService.getAllRoles();
        return GenericResponse.ok("success.roles.fetched", roles);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get role by ID")
    public ResponseEntity<GenericResponse<ViewRoleDTO.Output>> getRoleById(@PathVariable UUID id) {
        ViewRoleDTO.Output role = roleService.getRoleById(id);
        return GenericResponse.ok("success.role.fetched", role);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete role")
    public ResponseEntity<GenericResponse<Void>> deleteRole(
            @PathVariable UUID id,
            @RequestBody(required = false) DeleteRoleDTO.Input dto) {
        roleService.deleteRole(id, dto);
        return GenericResponse.ok("success.role.deleted");
    }

}
