package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import minaloc.mbaza.api.common.annotations.ApplySecurityRequirements;
import minaloc.mbaza.api.iam.services.RoleService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/roles")
@ApplySecurityRequirements
@Tag(name = "Roles management")
@Validated
public class RoleController {

}
