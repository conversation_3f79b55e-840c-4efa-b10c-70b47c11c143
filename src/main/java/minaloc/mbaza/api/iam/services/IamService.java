package minaloc.mbaza.api.iam.services;

import minaloc.mbaza.api.iam.dtos.LoginDTO;
import minaloc.mbaza.api.iam.dtos.RegisterDTO;
import minaloc.mbaza.api.iam.dtos.RequestPasswordResetCodeDTO;
import minaloc.mbaza.api.iam.dtos.ResetPasswordDTO;

public interface IamService {
    void registerUser(RegisterDTO.Input dto);

    LoginDTO.Output login(LoginDTO.Input dto);

    void requestPasswordResetCode(RequestPasswordResetCodeDTO.Input dto);

    void resetPassword(ResetPasswordDTO.Input dto);
}
