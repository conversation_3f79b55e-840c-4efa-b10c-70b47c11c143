package minaloc.mbaza.api.iam.services.impl;

import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.services.RoleService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoleServiceImpl implements RoleService {
    @Override
    public void createRole(CreateRoleDTO.Input dto) {

    }

    @Override
    public void updateRole(Long id, CreateRoleDTO.Input dto) {

    }

    @Override
    public List<ViewRoleDTO.Output> fetchManagedRoles() {
        return List.of();
    }

    @Override
    public void deleteRole(Long id, DeleteRoleDTO.Input dto) {

    }

    @Override
    public void toggleRoleStatus(Long id) {

    }
}
