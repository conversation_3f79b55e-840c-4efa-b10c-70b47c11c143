package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.services.RoleService;
import minaloc.mbaza.api.iam.usecases.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final CreateRoleUseCase createRoleUseCase;
    private final UpdateRoleUseCase updateRoleUseCase;
    private final FetchManagedRolesUseCase fetchManagedRolesUseCase;
    private final DeleteRoleUseCase deleteRoleUseCase;
    private final ToggleRoleStatusUseCase toggleRoleStatusUseCase;

    @Override
    public void createRole(CreateRoleDTO.Input dto) {
        createRoleUseCase.execute(dto);
    }

    @Override
    public void updateRole(UUID id, CreateRoleDTO.Input dto) {
        updateRoleUseCase.execute(id, dto);
    }

    @Override
    public List<ViewRoleDTO.Output> fetchManagedRoles() {
        return fetchManagedRolesUseCase.execute();
    }

    @Override
    public void deleteRole(UUID id, DeleteRoleDTO.Input dto) {
        deleteRoleUseCase.execute(id, dto);
    }

    @Override
    public void toggleRoleStatus(UUID id) {
        toggleRoleStatusUseCase.execute(id);
    }
}
