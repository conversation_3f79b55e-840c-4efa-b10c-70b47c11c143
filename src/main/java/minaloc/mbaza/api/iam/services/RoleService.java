package minaloc.mbaza.api.iam.services;

import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;

import java.util.List;

public interface RoleService {
    void createRole(CreateRoleDTO.Input dto);
    
    void updateRole(Long id, CreateRoleDTO.Input dto);
    
    List<ViewRoleDTO.Output> fetchManagedRoles();
    
    void deleteRole(Long id, DeleteRoleDTO.Input dto);
    
    void toggleRoleStatus(Long id);
}