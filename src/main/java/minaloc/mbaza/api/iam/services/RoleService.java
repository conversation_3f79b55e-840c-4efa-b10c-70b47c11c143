package minaloc.mbaza.api.iam.services;

import minaloc.mbaza.api.iam.dtos.CreateRoleDTO;
import minaloc.mbaza.api.iam.dtos.DeleteRoleDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;

import java.util.List;
import java.util.UUID;

public interface RoleService {
    void createRole(CreateRoleDTO.Input dto);

    void updateRole(UUID id, CreateRoleDTO.Input dto);

    List<ViewRoleDTO.Output> fetchManagedRoles();

    void deleteRole(UUID id, DeleteRoleDTO.Input dto);

    void toggleRoleStatus(UUID id);
}