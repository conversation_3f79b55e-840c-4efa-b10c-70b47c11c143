package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.*;
import minaloc.mbaza.api.iam.services.IamService;
import minaloc.mbaza.api.iam.usecases.*;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IamServiceImpl implements IamService {

    private final RegisterUserUseCase registerUserUseCase;
    private final LoginUseCase loginUseCase;
    private final RefreshTokenUseCase refreshTokenUseCase;
    private final RequestPasswordResetUseCase requestPasswordResetUseCase;
    private final ResetPasswordUseCase resetPasswordUseCase;

    @Override
    public void registerUser(RegisterDTO.Input dto) {
        registerUserUseCase.registerUser(dto);
    }

    @Override
    public LoginDTO.Output login(LoginDTO.Input dto) {
        return loginUseCase.login(dto);
    }

    @Override
    public RefreshTokenDTO.Output refreshToken(RefreshTokenDTO.Input dto) {
        return refreshTokenUseCase.refreshToken(dto);
    }

    @Override
    public void requestPasswordResetCode(RequestPasswordResetCodeDTO.Input dto) {
        requestPasswordResetUseCase.requestPasswordReset(dto);
    }

    @Override
    public void resetPassword(ResetPasswordDTO.Input dto) {
        resetPasswordUseCase.resetPassword(dto);
    }
}
