package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.LoginDTO;
import minaloc.mbaza.api.iam.dtos.RegisterDTO;
import minaloc.mbaza.api.iam.dtos.RequestPasswordResetCodeDTO;
import minaloc.mbaza.api.iam.dtos.ResetPasswordDTO;
import minaloc.mbaza.api.iam.services.IamService;
import minaloc.mbaza.api.iam.usecases.LoginUseCase;
import minaloc.mbaza.api.iam.usecases.RegisterUserUseCase;
import minaloc.mbaza.api.iam.usecases.RequestPasswordResetUseCase;
import minaloc.mbaza.api.iam.usecases.ResetPasswordUseCase;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IamServiceImpl implements IamService {

    private final RegisterUserUseCase registerUserUseCase;
    private final LoginUseCase loginUseCase;
    private final RequestPasswordResetUseCase requestPasswordResetUseCase;
    private final ResetPasswordUseCase resetPasswordUseCase;

    @Override
    public void registerUser(RegisterDTO.Input dto) {
        registerUserUseCase.execute(dto);
    }

    @Override
    public LoginDTO.Output login(LoginDTO.Input dto) {
        return loginUseCase.execute(dto);
    }

    @Override
    public void requestPasswordResetCode(RequestPasswordResetCodeDTO.Input dto) {
        requestPasswordResetUseCase.execute(dto);
    }

    @Override
    public void resetPassword(ResetPasswordDTO.Input dto) {
        resetPasswordUseCase.execute(dto);
    }
}
