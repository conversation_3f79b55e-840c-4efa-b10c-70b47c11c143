package minaloc.mbaza.api.iam.services;

import minaloc.mbaza.api.iam.dtos.CreateUserDTO;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;

import java.util.List;
import java.util.UUID;

public interface UserService {
    void createUser(CreateUserDTO.Input dto);

    void updateUser(UUID id, UpdateUserDTO.Input dto);

    List<ViewUserDTO.Output> getAllUsers();

    List<ViewUserDTO.Output> searchUsers(String searchTerm);

    ViewUserDTO.Output getUserById(UUID id);

    void deleteUser(UUID id);
}
