package minaloc.mbaza.api.iam.services.impl;

import minaloc.mbaza.api.iam.dtos.FetchPermissionsDTO;
import minaloc.mbaza.api.iam.dtos.ViewRoleDTO;
import minaloc.mbaza.api.iam.services.PermissionService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PermissionServiceImpl implements PermissionService {
    @Override
    public List<ViewRoleDTO.Output.ViewPermissionDto> fetchPermissions() {
        return List.of();
    }

    @Override
    public List<FetchPermissionsDTO.Output> fetchAdminPermissions() {
        return List.of();
    }
}
