package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.CreatePermissionDTO;
import minaloc.mbaza.api.iam.dtos.ViewPermissionDTO;
import minaloc.mbaza.api.iam.services.PermissionService;
import minaloc.mbaza.api.iam.usecases.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final CreatePermissionUseCase createPermissionUseCase;
    private final UpdatePermissionUseCase updatePermissionUseCase;
    private final GetAllPermissionsUseCase getAllPermissionsUseCase;
    private final GetPermissionByIdUseCase getPermissionByIdUseCase;
    private final DeletePermissionUseCase deletePermissionUseCase;

    @Override
    public void createPermission(CreatePermissionDTO.Input dto) {
        createPermissionUseCase.execute(dto);
    }

    @Override
    public void updatePermission(UUID id, CreatePermissionDTO.Input dto) {
        updatePermissionUseCase.execute(id, dto);
    }

    @Override
    public List<ViewPermissionDTO.Output> getAllPermissions() {
        return getAllPermissionsUseCase.execute();
    }

    @Override
    public ViewPermissionDTO.Output getPermissionById(UUID id) {
        return getPermissionByIdUseCase.execute(id);
    }

    @Override
    public void deletePermission(UUID id) {
        deletePermissionUseCase.execute(id);
    }
}
