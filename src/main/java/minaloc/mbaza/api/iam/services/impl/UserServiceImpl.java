package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.CreateUserDTO;
import minaloc.mbaza.api.iam.dtos.UpdateUserDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserDTO;
import minaloc.mbaza.api.iam.services.UserService;
import minaloc.mbaza.api.iam.usecases.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final CreateUserUseCase createUserUseCase;
    private final UpdateUserUseCase updateUserUseCase;
    private final GetAllUsersUseCase getAllUsersUseCase;
    private final SearchUsersUseCase searchUsersUseCase;
    private final GetUserByIdUseCase getUserByIdUseCase;
    private final DeleteUserUseCase deleteUserUseCase;
    private final GetCurrentUserUseCase getCurrentUserUseCase;
    private final UpdateCurrentUserUseCase updateCurrentUserUseCase;

    @Override
    public void createUser(CreateUserDTO.Input dto) {
        createUserUseCase.execute(dto);
    }

    @Override
    public void updateUser(UUID id, UpdateUserDTO.Input dto) {
        updateUserUseCase.execute(id, dto);
    }

    @Override
    public List<ViewUserDTO.Output> getAllUsers() {
        return getAllUsersUseCase.execute();
    }

    @Override
    public List<ViewUserDTO.Output> searchUsers(String searchTerm) {
        return searchUsersUseCase.execute(searchTerm);
    }

    @Override
    public ViewUserDTO.Output getUserById(UUID id) {
        return getUserByIdUseCase.execute(id);
    }

    @Override
    public void deleteUser(UUID id) {
        deleteUserUseCase.execute(id);
    }

    @Override
    public ViewUserDTO.Output getCurrentUser() {
        return getCurrentUserUseCase.execute();
    }

    @Override
    public void updateCurrentUser(UpdateUserDTO.Input dto) {
        updateCurrentUserUseCase.execute(dto);
    }
}
