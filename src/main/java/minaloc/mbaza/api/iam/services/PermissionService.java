package minaloc.mbaza.api.iam.services;

import minaloc.mbaza.api.iam.dtos.CreatePermissionDTO;
import minaloc.mbaza.api.iam.dtos.ViewPermissionDTO;

import java.util.List;
import java.util.UUID;

public interface PermissionService {
    void createPermission(CreatePermissionDTO.Input dto);

    void updatePermission(UUID id, CreatePermissionDTO.Input dto);

    List<ViewPermissionDTO.Output> getAllPermissions();

    ViewPermissionDTO.Output getPermissionById(UUID id);

    void deletePermission(UUID id);
}